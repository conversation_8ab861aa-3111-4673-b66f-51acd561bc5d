import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ArrowDown, Github, Linkedin, Download, MessageCircle } from 'lucide-react';
import profileImage from '@/assets/profile-image.jpg';
import { getReq } from '@/api/apiService';

interface Profile {
  id: number;
  name: string;
  title: string;
  bio: string;
  email: string;
  phone: string;
  location: string;
  github_url: string;
  linkedin_url: string;
  twitter_url: string;
  resume_url: string;
  profile_image: string | null;
  years_experience: number;
  projects_completed: number;
  technologies_mastered: number;
  client_satisfaction: number;
  available_for_work: boolean;
}

interface HeroProps {
  onScrollToProjects: () => void;
  onScrollToContact: () => void;
}

const Hero: React.FC<HeroProps> = React.memo(({ onScrollToProjects, onScrollToContact }) => {
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const [currentText, setCurrentText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);

  // Default texts if no profile data is available
  const defaultTexts = ['Full Stack Developer', 'React Expert', 'Node.js Developer', 'UI/UX Enthusiast'];
  const texts = profile ? [profile.title, 'Full Stack Developer', 'Software Engineer', 'Problem Solver'] : defaultTexts;

  const typingSpeed = 100;
  const deletingSpeed = 50;
  const pauseTime = 2000;

  // Fetch profile data
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setLoading(true);
        const profileData = await getReq('profile/current/');
        if (profileData) {
          setProfile(profileData);
        }
      } catch (error) {
        console.error('Error fetching profile data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);

  // Typing animation effect
  useEffect(() => {
    const handleTyping = () => {
      const fullText = texts[currentTextIndex];
      
      if (isDeleting) {
        setCurrentText(fullText.substring(0, currentText.length - 1));
        
        if (currentText === '') {
          setIsDeleting(false);
          setCurrentTextIndex((prevIndex) => (prevIndex + 1) % texts.length);
        }
      } else {
        setCurrentText(fullText.substring(0, currentText.length + 1));
        
        if (currentText === fullText) {
          setTimeout(() => setIsDeleting(true), pauseTime);
          return;
        }
      }
    };

    const timeout = setTimeout(handleTyping, isDeleting ? deletingSpeed : typingSpeed);
    return () => clearTimeout(timeout);
  }, [currentText, isDeleting, currentTextIndex, texts]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const leftVariants = {
    hidden: { x: -100, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
        damping: 12,
      },
    },
  };

  const rightVariants = {
    hidden: { x: 100, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
        damping: 12,
        delay: 0.2,
      },
    },
  };

  return (
    <section id="hero" className="min-h-screen flex items-center justify-center relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-hero opacity-10" />

      {/* Loading State */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-20">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading profile...</p>
          </div>
        </div>
      )}
      
      <motion.div
        className="container mx-auto px-4 relative z-10"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          {/* Left Side - Profile Image (35% of screen) */}
          <motion.div 
            variants={leftVariants} 
            className="flex justify-center lg:justify-end"
          >
            <div className="relative">
              {/* Main Profile Image */}
              <motion.div
                className="relative w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden border-4 border-primary/30 shadow-2xl"
                whileHover={{ scale: 1.05 }}
                animate={{ 
                  boxShadow: [
                    '0 0 30px hsl(var(--primary) / 0.3)', 
                    '0 0 50px hsl(var(--primary) / 0.6)', 
                    '0 0 30px hsl(var(--primary) / 0.3)'
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity, ease: 'easeInOut' }}
              >
                <img
                  src={profile?.profile_image || profileImage}
                  alt={profile?.name || "Nur Pratap Karki"}
                  className="w-full h-full object-cover"
                />
                <motion.div
                  className="absolute inset-0 bg-gradient-primary opacity-0 hover:opacity-20 transition-opacity duration-300"
                  whileHover={{ opacity: 0.2 }}
                />
              </motion.div>

              {/* Interactive Floating Elements */}
              <motion.div
                className="absolute -top-4 -right-4 w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center cursor-pointer"
                whileHover={{ scale: 1.2, rotate: 10 }}
                whileTap={{ scale: 0.9 }}
                animate={{
                  y: [0, -10, 0],
                  rotate: [0, 5, 0],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: 'easeInOut',
                }}
              >
                <Github className="w-8 h-8 text-primary" />
              </motion.div>

              <motion.div
                className="absolute -bottom-4 -left-4 w-16 h-16 bg-accent/20 rounded-full flex items-center justify-center cursor-pointer"
                whileHover={{ scale: 1.2, rotate: -10 }}
                whileTap={{ scale: 0.9 }}
                animate={{
                  y: [0, 10, 0],
                  rotate: [0, -5, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: 'easeInOut',
                  delay: 1,
                }}
              >
                <Linkedin className="w-8 h-8 text-accent" />
              </motion.div>

              <motion.div
                className="absolute top-1/2 -left-8 w-12 h-12 bg-secondary/20 rounded-full flex items-center justify-center cursor-pointer"
                whileHover={{ scale: 1.3 }}
                whileTap={{ scale: 0.8 }}
                animate={{
                  x: [0, -5, 0],
                  scale: [1, 1.1, 1],
                }}
                transition={{
                  duration: 2.5,
                  repeat: Infinity,
                  ease: 'easeInOut',
                  delay: 0.5,
                }}
              >
                <Download className="w-6 h-6 text-secondary-foreground" />
              </motion.div>

              <motion.div
                className="absolute top-1/2 -right-8 w-12 h-12 bg-muted/30 rounded-full flex items-center justify-center cursor-pointer"
                whileHover={{ scale: 1.3 }}
                whileTap={{ scale: 0.8 }}
                animate={{
                  x: [0, 5, 0],
                  scale: [1, 1.1, 1],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: 'easeInOut',
                  delay: 1.5,
                }}
              >
                <MessageCircle className="w-6 h-6 text-muted-foreground" />
              </motion.div>

              {/* Decorative Rings */}
              <motion.div
                className="absolute inset-0 w-full h-full border border-primary/20 rounded-full"
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
                style={{ scale: 1.1 }}
              />
              <motion.div
                className="absolute inset-0 w-full h-full border border-accent/20 rounded-full"
                animate={{ rotate: -360 }}
                transition={{ duration: 25, repeat: Infinity, ease: 'linear' }}
                style={{ scale: 1.2 }}
              />
            </div>
          </motion.div>

          {/* Right Side - Content */}
          <motion.div variants={rightVariants} className="space-y-8">
            <div>
              <motion.h1
                className="text-4xl md:text-6xl lg:text-7xl font-bold mb-4"
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.8, type: 'spring' }}
              >
                {loading ? (
                  <>
                    Hello, I'm{' '}
                    <span className="gradient-text block lg:inline">
                      Loading...
                    </span>
                  </>
                ) : (
                  <>
                    Hello, I'm{' '}
                    <span className="gradient-text block lg:inline">
                      {profile?.name || 'Developer'}
                    </span>
                  </>
                )}
              </motion.h1>
              
              <h2 className="text-2xl md:text-3xl lg:text-4xl text-muted-foreground font-light mb-6">
                <span className="typing-text">
                  {currentText}
                </span>
              </h2>
            </div>

            <p className="text-lg md:text-xl text-muted-foreground leading-relaxed max-w-2xl">
              {profile?.bio || 'Passionate about creating beautiful, functional web applications with modern technologies. Specializing in React, Node.js, and cloud solutions.'}
            </p>

            <div className="flex flex-col sm:flex-row gap-4">
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  onClick={onScrollToProjects}
                  variant="hero"
                  size="lg"
                  className="shadow-[0_0_30px_hsl(var(--primary)/0.3)] hover:shadow-[0_0_40px_hsl(var(--primary)/0.5)]"
                >
                  View My Projects
                </Button>
              </motion.div>
              
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  onClick={onScrollToContact}
                  variant="outline_hero"
                  size="lg"
                  className="shadow-[0_0_20px_hsl(var(--primary)/0.2)] hover:shadow-[0_0_30px_hsl(var(--primary)/0.4)]"
                >
                  Get In Touch
                </Button>
              </motion.div>
            </div>

            <div className="flex space-x-6">
              {profile?.github_url && (
                <motion.a
                  href={profile.github_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-3 rounded-full bg-card hover:bg-primary hover:text-primary-foreground transition-colors shadow-lg"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Github className="w-6 h-6" />
                </motion.a>
              )}

              {profile?.linkedin_url && (
                <motion.a
                  href={profile.linkedin_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-3 rounded-full bg-card hover:bg-primary hover:text-primary-foreground transition-colors shadow-lg"
                  whileHover={{ scale: 1.1, rotate: -5 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Linkedin className="w-6 h-6" />
                </motion.a>
              )}
            </div>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          animate={{
            y: [0, 10, 0],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        >
          <ArrowDown className="w-6 h-6 text-primary" />
        </motion.div>
      </motion.div>

      {/* Background Decorative Elements */}
      <motion.div
        className="absolute top-20 left-20 w-20 h-20 bg-primary/10 rounded-full"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />
      
      <motion.div
        className="absolute bottom-20 right-20 w-16 h-16 bg-accent/10 rounded-full"
        animate={{
          y: [0, 15, 0],
          x: [0, -15, 0],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: 'easeInOut',
          delay: 1,
        }}
      />
    </section>
  );
});

Hero.displayName = 'Hero';

export default Hero;