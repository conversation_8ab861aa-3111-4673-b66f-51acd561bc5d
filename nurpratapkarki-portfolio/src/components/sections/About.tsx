import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Download } from 'lucide-react';
import { getReq } from '@/api/apiService';

interface Skill {
  id: number;
  name: string;
  proficiency: number;
  years_experience: number;
}

interface SkillCategory {
  id: number;
  name: string;
  color: string;
  technologies: string[];
  skills: Skill[];
  order: number;
}

interface Experience {
  id: number;
  title: string;
  company: string;
  period: string;
  description: string;
  technologies: string[];
  order: number;
}

interface Education {
  id: number;
  degree: string;
  institution: string;
  period: string;
  description: string;
  order: number;
}

interface Profile {
  id: number;
  name: string;
  title: string;
  bio: string;
  email: string;
  phone: string;
  location: string;
  github_url: string;
  linkedin_url: string;
  twitter_url: string;
  resume_url: string;
  profile_image: string | null;
  years_experience: number;
  projects_completed: number;
  technologies_mastered: number;
  client_satisfaction: number;
  available_for_work: boolean;
}

const About: React.FC = () => {
  const [skillCategories, setSkillCategories] = useState<SkillCategory[]>([]);
  const [experiences, setExperiences] = useState<Experience[]>([]);
  const [education, setEducation] = useState<Education[]>([]);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Fetch data from API
  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch data individually to avoid Promise.all failing if one request fails
        try {
          const categoriesData = await getReq('skills/categories/');
          if (isMounted && categoriesData) {
            setSkillCategories(Array.isArray(categoriesData) ? categoriesData : categoriesData.results || []);
          }
        } catch (error) {
          console.error('Error fetching skill categories:', error);
        }

        try {
          const experiencesData = await getReq('experiences/');
          if (isMounted && experiencesData) {
            setExperiences(Array.isArray(experiencesData) ? experiencesData : experiencesData.results || []);
          }
        } catch (error) {
          console.error('Error fetching experiences:', error);
        }

        try {
          const educationData = await getReq('education/');
          if (isMounted && educationData) {
            setEducation(Array.isArray(educationData) ? educationData : educationData.results || []);
          }
        } catch (error) {
          console.error('Error fetching education:', error);
        }

        try {
          const profileData = await getReq('profile/current/');
          if (isMounted && profileData) {
            setProfile(profileData);
          }
        } catch (error) {
          console.error('Error fetching profile:', error);
        }

      } catch (error) {
        console.error('Error in fetchData:', error);
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchData();

    // Cleanup function to prevent state updates if component unmounts
    return () => {
      isMounted = false;
    };
  }, []);

  // Get all skills from categories for the skills section
  const allSkills = skillCategories.flatMap(category => category.skills);
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
        damping: 12,
      },
    },
  };

  const handleDownloadResume = () => {
    if (profile?.resume_url) {
      const link = document.createElement('a');
      link.href = profile.resume_url;
      link.download = `${profile.name}_Resume.pdf`;
      link.target = '_blank';
      link.click();
    } else {
      // Fallback if no resume URL is available
      alert('Resume not available for download at the moment.');
    }
  };

  return (
    <section id="about" className="py-24">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4 gradient-text">
            About Me
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {profile ? profile.bio : 'Passionate full-stack developer with experience creating scalable web applications'}
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Personal Info */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="text-2xl">Who I Am</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {loading ? (
                  <div className="text-center py-4">
                    <p className="text-muted-foreground">Loading profile...</p>
                  </div>
                ) : (
                  <>
                    <p className="text-muted-foreground leading-relaxed">
                      {profile?.bio || "I'm a passionate full-stack developer who loves creating beautiful, functional web applications."}
                    </p>
                    <div className="grid grid-cols-2 gap-4 pt-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-primary">{profile?.years_experience || 0}+</div>
                        <div className="text-sm text-muted-foreground">Years Experience</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-primary">{profile?.projects_completed || 0}+</div>
                        <div className="text-sm text-muted-foreground">Projects Completed</div>
                      </div>
                    </div>
                    <div className="pt-4">
                      <Button
                        onClick={handleDownloadResume}
                        variant="hero"
                      >
                        <Download className="w-4 h-4 mr-2" />
                        Download Resume
                      </Button>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Skills */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="text-2xl">Technical Skills</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {loading ? (
                  <div className="text-center py-4">
                    <p className="text-muted-foreground">Loading skills...</p>
                  </div>
                ) : (
                  allSkills.map((skill, index) => (
                    <motion.div
                      key={skill.id}
                      initial={{ opacity: 0, x: 20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                    >
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium">{skill.name}</span>
                        <Badge variant="secondary" className="text-xs">
                          {skill.proficiency}%
                        </Badge>
                      </div>
                      <Progress
                        value={skill.proficiency}
                        className="h-2 bg-muted"
                      />
                    </motion.div>
                  ))
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Experience Timeline */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold mb-4">Experience</h3>
            <p className="text-muted-foreground">My professional journey</p>
          </div>

          <motion.div
            className="space-y-8"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {experiences.map((experience, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="relative"
              >
                <Card className="glass-card ml-8 relative">
                  {/* Timeline connector */}
                  <div className="absolute -left-8 top-6 w-4 h-4 bg-gradient-primary rounded-full border-4 border-background" />
                  {index < experiences.length - 1 && (
                    <div className="absolute -left-6 top-10 w-0.5 h-full bg-border" />
                  )}

                  <CardHeader>
                    <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-2">
                      <div>
                        <CardTitle className="text-xl">{experience.title}</CardTitle>
                        <p className="text-primary font-medium">{experience.company}</p>
                      </div>
                      <Badge variant="outline" className="text-sm w-fit">
                        {experience.period}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground mb-4 leading-relaxed">
                      {experience.description}
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {experience.technologies.map((tech) => (
                        <Badge key={tech} variant="secondary" className="text-xs">
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;