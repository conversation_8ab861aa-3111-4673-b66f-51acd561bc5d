import React, { useEffect, useState } from 'react';
import { getReq } from '@/api/apiService';

const ApiTest: React.FC = () => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  console.log('ApiTest component rendered');

  // Test if component is actually mounting
  useEffect(() => {
    console.log('ApiTest useEffect triggered');
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Starting API test...');
        setLoading(true);
        setError(null);

        // Test direct fetch first
        console.log('Testing direct fetch...');
        const directResponse = await fetch('/api/profile/current/');
        console.log('Direct fetch response:', directResponse);
        const directData = await directResponse.json();
        console.log('Direct fetch data:', directData);

        // Test getReq function
        console.log('Testing getReq function...');
        const result = await getReq('profile/current/');
        console.log('getReq result:', result);

        if (result) {
          setData(result);
          console.log('Data set successfully:', result);
        } else {
          setError('No data returned from getReq');
          console.log('No data returned from getReq');
        }
      } catch (err) {
        console.error('Error in API test:', err);
        setError(`Error: ${err}`);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div style={{ padding: '20px', border: '5px solid red', margin: '20px', backgroundColor: 'yellow', zIndex: 9999, position: 'relative' }}>
      <h2 style={{ color: 'black', fontSize: '24px', fontWeight: 'bold' }}>API Test Component - VISIBLE</h2>
      <p>Loading: {loading ? 'Yes' : 'No'}</p>
      <p>Error: {error || 'None'}</p>
      <p>Data: {data ? JSON.stringify(data, null, 2) : 'No data'}</p>
      {data && (
        <div>
          <h3>Profile Data:</h3>
          <p>Name: {data.name}</p>
          <p>Title: {data.title}</p>
          <p>Bio: {data.bio}</p>
        </div>
      )}
    </div>
  );
};

export default ApiTest;
