import React, { useEffect, useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { ThemeProvider } from '@/contexts/ThemeContext';
import AnimatedBackground from '@/components/AnimatedBackground';
import HackerBackground from '@/components/HackerBackground';
import MouseTrail from '@/components/MouseTrail';
import Navigation from '@/components/Navigation';
import Hero from '@/components/sections/Hero';
import Projects from '@/components/sections/Projects';
import About from '@/components/sections/About';
import Skills from '@/components/sections/Skills';
import Blog from '@/components/sections/Blog';
import Testimonials from '@/components/sections/Testimonials';
import Contact from '@/components/sections/Contact';
import MobileTabBar from '@/components/MobileTabBar';
import ApiTest from '@/components/ApiTest';

const Index = () => {
  const [activeSection, setActiveSection] = useState('hero');
  const navigate = useNavigate();

  // Intersection Observer for section tracking
  useEffect(() => {
    const sections = ['hero', 'projects', 'about', 'skills', 'blog', 'testimonials', 'contact'];
    const observers = new Map();

    const observerOptions = {
      root: null,
      rootMargin: '-50% 0px -50% 0px',
      threshold: 0,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          setActiveSection(entry.target.id);
        }
      });
    };

    sections.forEach((sectionId) => {
      const section = document.getElementById(sectionId);
      if (section) {
        const observer = new IntersectionObserver(observerCallback, observerOptions);
        observer.observe(section);
        observers.set(sectionId, observer);
      }
    });

    return () => {
      observers.forEach((observer) => observer.disconnect());
    };
  }, []);

  const scrollToSection = useCallback((sectionId: string) => {
    const element = document.getElementById(sectionId);
    element?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  const handleScrollToProjects = useCallback(() => scrollToSection('projects'), [scrollToSection]);
  const handleScrollToContact = useCallback(() => scrollToSection('contact'), [scrollToSection]);

  return (
    <ThemeProvider>
      <div className="min-h-screen bg-background text-foreground">
        <AnimatedBackground />
        <HackerBackground />
        <MouseTrail />
        
        <Navigation 
          activeSection={activeSection} 
          onSectionClick={scrollToSection} 
        />

        <main>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <Hero
              onScrollToProjects={handleScrollToProjects}
              onScrollToContact={handleScrollToContact}
            />
            
            {/* Section Divider */}
            <div className="h-px bg-gradient-primary opacity-30 mx-auto max-w-xs"></div>
            
            <Projects />
            
            {/* Section Divider */}
            <div className="h-px bg-gradient-primary opacity-30 mx-auto max-w-xs"></div>
            
            <About />
            
            {/* Section Divider */}
            <div className="h-px bg-gradient-primary opacity-30 mx-auto max-w-xs"></div>
            
            <Skills />
            
            {/* Section Divider */}
            <div className="h-px bg-gradient-primary opacity-30 mx-auto max-w-xs"></div>
            
            <Blog />
            
            {/* Section Divider */}
            <div className="h-px bg-gradient-primary opacity-30 mx-auto max-w-xs"></div>
            
            <Testimonials />
            
            {/* Section Divider */}
            <div className="h-px bg-gradient-primary opacity-30 mx-auto max-w-xs"></div>
            
            <Contact />
          </motion.div>
        </main>

        <MobileTabBar onSectionClick={scrollToSection} />
      </div>
    </ThemeProvider>
  );
};

export default Index;
